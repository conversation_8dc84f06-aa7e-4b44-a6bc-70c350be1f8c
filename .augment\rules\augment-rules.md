---
type: "agent_requested"
description: "Example description"
---
**`duix.mobile` 开源项目二次开发指南**

作为 `duix.mobile` 开源项目的开发代理，您必须严格遵守以下开发规范。

**I. 项目基础与环境配置**

1.  **操作系统环境:** 明确并始终记住，所有开发活动和命令执行都将在 **Windows 操作系统**环境下进行。在处理文件路径、执行命令或配置工具时，请务必遵循Windows的约定。
2.  **项目文档查阅:** 在开始任何开发任务之前，您必须首先彻底阅读并理解项目根目录下的 `wiki` 文件。此文件是您获取项目特定信息、架构概述和现有功能细节的主要来源。
      * *工具辅助:* 如果 `wiki` 内容需要从外部源获取，请使用 `fetch` 工具。对于复杂的文档内容，可以利用 `Sequential thinking` 工具来帮助您结构化地理解和梳理信息。
3.  **Java 版本规定:** 项目强制使用 **Java17** 作为开发和运行环境。请确保所有代码的编译、依赖管理和运行时环境都与此版本兼容。

**II. 代码质量与架构原则**

4.  **模块化与解耦优先:** 在设计和实现新功能时，务必高度重视模块化和职责分离。将复杂的功能拆解为更小、独立、可重用的组件。坚决避免将所有功能集中在一个文件中实现，力求高内聚和低耦合的架构。
5.  **高质量代码实践:** 编写清晰、可读、结构良好且易于维护的代码。遵循标准的Java编码约定，并尽可能应用设计原则（如SOLID）。注释应解释代码背后的“为什么”，而不仅仅是“做什么”。
6.  **新建文件头部注释:** 您创建的每一个新的源代码文件（例如 `.java` 文件）都必须在文件顶部包含一个标准的注释块。此注释块应清晰地描述该文件的主要功能和用途。
    ```java
    // File: [文件路径，例如 src/main/java/com/duix/mobile/feature/NewComponent.java]
    // Description: 本文件负责实现 [具体的功能描述，例如用户认证逻辑的入口点]。
    // Purpose: [该文件在整个模块或系统中的作用].
    // Date: YYYY-MM-DD
    ```

**III. 开发流程与任务管理**

7.  **待办事项驱动开发:** 对于每一个开发需求或功能点，您必须首先明确定义一系列可操作的待办事项或子任务。
      * 在开始任何任务之前，请明确其“完成定义”（Definition of Done）。
      * 按照任务的依赖关系或优先级依次执行。
      * **任务结果更新:** 每完成一个待办事项，您必须在项目根目录下的 `task` 文件夹中更新对应的任务结果文件。此更新应包含已完成工作的简要总结、关键决策和任何相关输出。
8.  **MCP 工具策略性运用:**
      * **Google Search:** 利用 `Google search` 工具进行外部资料查询、概念澄清以及解决开发过程中遇到的未知问题。
      * **Context 7 (上下文管理):** 运用 `Context 7` 工具来维护和检索与当前开发任务相关的特定上下文信息，尤其是在处理复杂逻辑流或状态管理时。
      * **Sequential Thinking (顺序思考):** 运用 `Sequential thinking` 工具将复杂问题分解为逻辑上连续的步骤，确保以系统化的方法进行问题解决和方案设计。
      * **mcp-neo4j-memory (记忆与知识库):** 如果需要存储和查询项目组件之间的结构化知识、关系或历史决策以便长期参考，请考虑使用 `mcp-neo4j-memory`。
      * **fetch (数据抓取):** 当开发过程需要获取外部数据或资源（例如API文档、代码示例、外部配置）时，请使用 `fetch` 工具。
